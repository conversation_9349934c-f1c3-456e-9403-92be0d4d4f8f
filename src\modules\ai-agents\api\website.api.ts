import { apiClient } from '@/shared/api';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * API functions cho Website integration
 * Tương ứng với AgentWebsiteController trong backend
 */

export interface WebsiteDto {
  id: string;
  domain: string;
  name: string;
  description?: string;
  favicon?: string;
  isActive: boolean;
  createdAt: number;
}

export interface AgentWebsiteDto {
  agentId: string;
  websiteId: string;
  website: WebsiteDto;
  isActive: boolean;
  settings?: {
    position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
    theme?: 'light' | 'dark' | 'auto';
    primaryColor?: string;
    welcomeMessage?: string;
    placeholder?: string;
  };
  widgetScript?: string;
  createdAt: number;
}

export interface WebsiteQueryDto extends QueryDto {
  search?: string;
  isActive?: boolean;
}

export interface ConnectWebsiteDto {
  websiteId: string;
  settings?: {
    position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
    theme?: 'light' | 'dark' | 'auto';
    primaryColor?: string;
    welcomeMessage?: string;
    placeholder?: string;
  };
}

export interface UpdateAgentWebsiteDto {
  isActive?: boolean;
  settings?: {
    position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
    theme?: 'light' | 'dark' | 'auto';
    primaryColor?: string;
    welcomeMessage?: string;
    placeholder?: string;
  };
}

/**
 * Lấy danh sách Websites có sẵn
 * GET /user/websites
 */
export const getAvailableWebsites = async (
  params?: WebsiteQueryDto
): Promise<ApiResponse<PaginatedResult<WebsiteDto>>> => {
  return apiClient.get('/user/websites', { params });
};

/**
 * Lấy danh sách Websites được kết nối với agent
 * GET /user/agents/{id}/websites
 */
export const getAgentWebsites = async (
  agentId: string,
  params?: QueryDto
): Promise<ApiResponse<PaginatedResult<AgentWebsiteDto>>> => {
  return apiClient.get(`/user/agents/${agentId}/websites`, { params });
};

/**
 * Kết nối Website với agent
 * POST /user/agents/{id}/websites
 */
export const connectWebsiteToAgent = async (
  agentId: string,
  data: ConnectWebsiteDto
): Promise<ApiResponse<AgentWebsiteDto>> => {
  return apiClient.post(`/user/agents/${agentId}/websites`, data);
};

/**
 * Cập nhật cài đặt Website của agent
 * PUT /user/agents/{agentId}/websites/{websiteId}
 */
export const updateAgentWebsite = async (
  agentId: string,
  websiteId: string,
  data: UpdateAgentWebsiteDto
): Promise<ApiResponse<AgentWebsiteDto>> => {
  return apiClient.put(`/user/agents/${agentId}/websites/${websiteId}`, data);
};

/**
 * Ngắt kết nối Website khỏi agent
 * DELETE /user/agents/{agentId}/websites/{websiteId}
 */
export const disconnectWebsiteFromAgent = async (
  agentId: string,
  websiteId: string
): Promise<ApiResponse<void>> => {
  return apiClient.delete(`/user/agents/${agentId}/websites/${websiteId}`);
};

/**
 * Lấy widget script cho website
 * GET /user/agents/{agentId}/websites/{websiteId}/script
 */
export const getWebsiteWidgetScript = async (
  agentId: string,
  websiteId: string
): Promise<ApiResponse<{ script: string }>> => {
  return apiClient.get(`/user/agents/${agentId}/websites/${websiteId}/script`);
};
