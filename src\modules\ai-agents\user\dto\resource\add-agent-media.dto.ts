import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, ArrayMinSize, ArrayMaxSize, ArrayUnique } from 'class-validator';

/**
 * DTO cho việc thêm media vào agent
 */
export class AddAgentMediaDto {
  /**
   * Danh sách ID của media
   */
  @ApiProperty({
    description: 'Danh sách ID của media (tối thiểu 1, tối đa 30)',
    example: ['m1e2d3i4a5-1', 'm1e2d3i4a5-2'],
    type: [String],
    minItems: 1,
    maxItems: 30,
  })
  @IsArray({ message: 'mediaIds phải là một mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 Media' })
  @ArrayMaxSize(30, { message: 'Không được vượt quá 30 Media' })
  @ArrayUnique({ message: 'Không được có Media ID trùng lặp' })
  @IsUUID('4', { each: true, message: 'Mỗi Media ID phải là UUID hợp lệ' })
  mediaIds: string[];
}
