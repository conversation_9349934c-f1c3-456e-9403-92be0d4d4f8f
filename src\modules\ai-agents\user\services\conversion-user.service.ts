import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { 
  AgentUserRepository
} from '@modules/agent/repositories';
import { Transactional } from 'typeorm-transactional';
import { 
  UpdateConversionDto,
  ConversionResponseDto
} from '../dto/conversion';
import { ConversionMapper } from '../mappers';
import { ConvertConfig } from '@modules/agent/interfaces/convert-config.interface';

/**
 * Service xử lý các thao tác liên quan đến conversion config của agent cho người dùng
 */
@Injectable()
export class ConversionUserService {
  private readonly logger = new Logger(ConversionUserService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
  ) { }

  /**
   * Lấy thông tin conversion config của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Thông tin conversion config
   */
  async getConversion(
    agentId: string,
    userId: number,
  ): Promise<ConversionResponseDto> {
    try {
      // Kiểm tra agent có thuộc về user không
      const result = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      const { agentUser } = result;

      // Lấy conversion config, nếu không có thì sử dụng mặc định
      const convertConfig = agentUser.convertConfig || [];

      // Chuyển đổi sang response DTO
      const response = ConversionMapper.toResponseDto(convertConfig);

      this.logger.log(`Lấy conversion config thành công cho agent ${agentId}`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy conversion config agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Cập nhật conversion config của agent
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @param updateDto Thông tin conversion config cần cập nhật
   * @returns Thông tin conversion config đã cập nhật
   */
  @Transactional()
  async updateConversion(
    agentId: string,
    userId: number,
    updateDto: UpdateConversionDto,
  ): Promise<ConversionResponseDto> {
    try {
      // Kiểm tra agent có thuộc về user không
      const result = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Chuyển đổi DTO sang entity format
      const updatedConvertConfig = ConversionMapper.fromDtoToEntity(updateDto.convertConfig);

      // Validate conversion config
      if (!ConversionMapper.validateConvertConfig(updatedConvertConfig)) {
        throw new AppException(
          AGENT_ERROR_CODES.INVALID_MULTI_AGENT_CONFIG, // Sử dụng error code có sẵn
          'Cấu hình conversion không hợp lệ'
        );
      }

      // Cập nhật conversion config trong database
      await this.agentUserRepository.createQueryBuilder()
        .update()
        .set({ 
          convertConfig: updatedConvertConfig
        })
        .where('id = :id', { id: agentId })
        .andWhere('userId = :userId', { userId })
        .execute();

      // Chuyển đổi sang response DTO với config đã cập nhật
      const response = ConversionMapper.toResponseDto(updatedConvertConfig);

      this.logger.log(`Cập nhật conversion config thành công cho agent ${agentId}`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật conversion config agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  /**
   * Reset conversion config về mặc định
   * @param agentId ID của agent
   * @param userId ID của người dùng
   * @returns Thông tin conversion config mặc định
   */
  @Transactional()
  async resetConversion(
    agentId: string,
    userId: number,
  ): Promise<ConversionResponseDto> {
    try {
      // Kiểm tra agent có thuộc về user không
      const result = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // Tạo conversion config mặc định
      const defaultConvertConfig = ConversionMapper.createDefaultConvertConfig();

      // Cập nhật conversion config trong database
      await this.agentUserRepository.createQueryBuilder()
        .update()
        .set({ 
          convertConfig: defaultConvertConfig
        })
        .where('id = :id', { id: agentId })
        .andWhere('userId = :userId', { userId })
        .execute();

      // Chuyển đổi sang response DTO
      const response = ConversionMapper.toResponseDto(defaultConvertConfig);

      this.logger.log(`Reset conversion config thành công cho agent ${agentId}`);
      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi reset conversion config agent ${agentId}: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED, error.message);
    }
  }

  // ==================== VALIDATION METHODS ====================

  /**
   * Validate conversion config có hợp lệ không
   * @param convertConfig Array ConvertConfig để validate
   * @returns boolean
   */
  private validateConvertConfigStructure(convertConfig: ConvertConfig[]): boolean {
    return ConversionMapper.validateConvertConfig(convertConfig);
  }
}
