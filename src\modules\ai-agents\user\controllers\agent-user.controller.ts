import { ApiResponseDto, PaginatedResult } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { AgentUserService } from '@modules/agent/user/services';
import { JwtUserGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { CreateAgentDto } from '../dto/agent/create-agent.dto';
import { CurrentUser } from '@/modules/auth/decorators';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { AGENT_ERROR_CODES } from '../../exceptions';
import { AgentListItemDto, AgentQueryDto, CreateAgentResponseDto } from '../dto/agent';
import { ErrorCode } from '@/common';
import { AgentSimpleListDto, AgentSimpleQueryDto } from '../dto';

/**
 * Controller xử lý các API endpoint cho Agent của người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto
)
export class AgentUserController {
  constructor(private readonly agentUserService: AgentUserService) { }

  /**
   * Lấy danh sách agent đơn giản (chỉ id, avatar, name)
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn với phân trang
   * @returns Danh sách agent đơn giản có phân trang
   */
  @Get('simple')
  @ApiOperation({ summary: 'Lấy danh sách agent đơn giản' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách agent đơn giản thành công',
    schema: ApiResponseDto.getPaginatedSchema(AgentSimpleListDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_LIST_QUERY_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getSimpleAgentList(
    @Query() queryDto: AgentSimpleQueryDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<PaginatedResult<AgentSimpleListDto>>> {
    const result = await this.agentUserService.getSimpleAgentList(userId, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách agent đơn giản thành công');
  }

  /**
   * Lấy danh sách agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent có phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách agent' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách agent thành công',
    schema: ApiResponseDto.getPaginatedSchema(AgentListItemDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_LIST_QUERY_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getAgents(
    @CurrentUser('id') userId: number,
    @Query() queryDto: AgentQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentListItemDto>>> {
    const result = await this.agentUserService.getAgents(userId, queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Tạo agent mới với cấu trúc modular
   * @param userId ID của người dùng
   * @param createDto Thông tin agent mới theo cấu hình TypeAgent
   * @returns Thông tin tạo agent thành công
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo agent mới',
    description: 'Tạo agent mới với cấu trúc modular theo cấu hình TypeAgent. Logic model: Bắt buộc có 1 trong 2 khối: (systemModelId) hoặc (userModelId + keyLlmId). Các khối khác có thể trống nếu không được enable trong TypeAgent config.'
  })
  @ApiResponse({
    status: 200,
    description: 'Tạo agent thành công',
    schema: ApiResponseDto.getSchema(CreateAgentResponseDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NAME_EXISTS,
    AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.INVALID_S3_KEY,
    AGENT_ERROR_CODES.INVALID_MODEL_CONFIG,
    AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
    AGENT_ERROR_CODES.AGENT_PROFILE_REQUIRED,
    AGENT_ERROR_CODES.AGENT_PROFILE_NOT_SUPPORTED,
    AGENT_ERROR_CODES.AGENT_OUTPUT_REQUIRED,
    AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
    AGENT_ERROR_CODES.AGENT_RESOURCES_NOT_SUPPORTED,
    AGENT_ERROR_CODES.AGENT_STRATEGY_NOT_SUPPORTED,
    AGENT_ERROR_CODES.AGENT_MULTI_AGENT_NOT_SUPPORTED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async createAgent(
    @CurrentUser('id') userId: number,
    @Body() createDto: CreateAgentDto,
  ) {
    const result = await this.agentUserService.createAgent(userId, createDto);
    return ApiResponseDto.success(result, 'Tạo agent thành công');
  }

  /**
   * Xóa agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @returns Thông báo thành công
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa agent' })
  @ApiResponse({
    status: 200,
    description: 'Xóa agent thành công',
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_DELETE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async deleteAgent(
    @CurrentUser('id') userId: number,
    @Param('id') id: string,
  ) {
    await this.agentUserService.deleteAgent(id, userId);
    return ApiResponseDto.success(null, 'Xóa agent thành công');
  }

  /**
   * Đảo ngược trạng thái hoạt động của agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @returns Thông tin cập nhật trạng thái thành công
   */
  @Patch(':id/active')
  @ApiOperation({ summary: 'Bật/tắt agent' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái agent thành công',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                active: {
                  type: 'boolean',
                  description: 'Trạng thái hoạt động mới',
                  example: true
                }
              }
            }
          }
        }
      ]
    }
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async updateAgentActive(
    @CurrentUser('id') userId: number,
    @Param('id') id: string,
  ) {
    const result = await this.agentUserService.updateAgentActive(id, userId);
    return ApiResponseDto.success(result, 'Cập nhật trạng thái agent thành công');
  }
}
