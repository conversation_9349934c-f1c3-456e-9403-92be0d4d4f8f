import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import {
  getAvailableStrategies,
  getAgentStrategies,
  assignStrategyToAgent,
  updateAgentStrategy,
  removeStrategyFromAgent,
  StrategyDto,
  AgentStrategyDto,
  StrategyQueryDto,
  AssignStrategyDto,
  UpdateAgentStrategyDto
} from '../api/strategy.api';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Hook để lấy danh sách strategies có sẵn
 */
export const useGetAvailableStrategies = (
  params?: StrategyQueryDto,
  options?: UseQueryOptions<ApiResponse<PaginatedResult<StrategyDto>>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.AVAILABLE_STRATEGIES, params],
    queryFn: () => getAvailableStrategies(params),
    staleTime: 10 * 60 * 1000, // 10 minutes (strategies ít thay đổi)
    ...options,
  });
};

/**
 * Hook để lấy danh sách strategies được gán cho agent
 */
export const useGetAgentStrategies = (
  agentId: string | undefined,
  params?: QueryDto,
  options?: UseQueryOptions<ApiResponse<PaginatedResult<AgentStrategyDto>>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.AGENT_STRATEGIES, agentId, params],
    queryFn: () => getAgentStrategies(agentId as string, params),
    enabled: !!agentId,
    staleTime: 5 * 60 * 1000,
    ...options,
  });
};

/**
 * Hook để gán strategy cho agent
 */
export const useAssignStrategyToAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.ASSIGN_STRATEGY],
    mutationFn: ({ agentId, data }: { agentId: string; data: AssignStrategyDto }) =>
      assignStrategyToAgent(agentId, data),
    onSuccess: (_, { agentId }) => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_STRATEGIES, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, agentId] });
    },
  });
};

/**
 * Hook để cập nhật strategy của agent
 */
export const useUpdateAgentStrategy = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.UPDATE_AGENT_STRATEGY],
    mutationFn: ({ 
      agentId, 
      strategyId, 
      data 
    }: { 
      agentId: string; 
      strategyId: string; 
      data: UpdateAgentStrategyDto 
    }) => updateAgentStrategy(agentId, strategyId, data),
    onSuccess: (_, { agentId }) => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_STRATEGIES, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, agentId] });
    },
  });
};

/**
 * Hook để xóa strategy khỏi agent
 */
export const useRemoveStrategyFromAgent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.REMOVE_STRATEGY],
    mutationFn: ({ agentId, strategyId }: { agentId: string; strategyId: string }) =>
      removeStrategyFromAgent(agentId, strategyId),
    onSuccess: (_, { agentId }) => {
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_STRATEGIES, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, agentId] });
    },
  });
};
