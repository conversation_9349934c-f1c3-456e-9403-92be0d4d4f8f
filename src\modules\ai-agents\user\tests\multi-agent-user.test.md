# Multi-Agent User API Tests

## Test Cases cho Multi-Agent System

### 1. Test GET `/user/agents/{id}/multi-agents`

#### Test Case 1.1: <PERSON><PERSON><PERSON> danh sách thành công
```bash
curl -X GET "http://localhost:3000/user/agents/550e8400-e29b-41d4-a716-446655440000/multi-agents?page=1&limit=10" \
  -H "Authorization: Bearer {JWT_TOKEN}" \
  -H "Content-Type: application/json"
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "childAgentId": "550e8400-e29b-41d4-a716-446655440001",
        "name": "Marketing Assistant",
        "avatar": "https://cdn.example.com/avatars/marketing.jpg",
        "prompt": "Bạn là trợ lý chuyên về marketing",
        "createdAt": 1672531200000
      }
    ],
    "meta": {
      "totalItems": 1,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

#### Test Case 1.2: Tìm kiếm theo tên agent
```bash
curl -X GET "http://localhost:3000/user/agents/550e8400-e29b-41d4-a716-446655440000/multi-agents?search=Marketing" \
  -H "Authorization: Bearer {JWT_TOKEN}"
```

#### Test Case 1.3: Tìm kiếm theo prompt
```bash
curl -X GET "http://localhost:3000/user/agents/550e8400-e29b-41d4-a716-446655440000/multi-agents?promptSearch=marketing" \
  -H "Authorization: Bearer {JWT_TOKEN}"
```

### 2. Test POST `/user/agents/{id}/multi-agents`

#### Test Case 2.1: Thêm agent con thành công
```bash
curl -X POST "http://localhost:3000/user/agents/550e8400-e29b-41d4-a716-446655440000/multi-agents" \
  -H "Authorization: Bearer {JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "multiAgents": [
      {
        "agent_id": "550e8400-e29b-41d4-a716-446655440001",
        "prompt": "Bạn là trợ lý chuyên về marketing"
      },
      {
        "agent_id": "550e8400-e29b-41d4-a716-446655440002",
        "prompt": "Bạn là trợ lý chuyên về kỹ thuật"
      }
    ]
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "idSuccess": ["550e8400-e29b-41d4-a716-446655440001", "550e8400-e29b-41d4-a716-446655440002"],
    "idFailed": [],
    "errors": {},
    "totalProcessed": 2,
    "successCount": 2,
    "failedCount": 0
  },
  "message": "Thêm agent con thành công"
}
```

#### Test Case 2.2: Self-reference error
```bash
curl -X POST "http://localhost:3000/user/agents/550e8400-e29b-41d4-a716-446655440000/multi-agents" \
  -H "Authorization: Bearer {JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "multiAgents": [
      {
        "agent_id": "550e8400-e29b-41d4-a716-446655440000",
        "prompt": "Self reference test"
      }
    ]
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "idSuccess": [],
    "idFailed": ["550e8400-e29b-41d4-a716-446655440000"],
    "errors": {
      "550e8400-e29b-41d4-a716-446655440000": "Agent không thể tham chiếu đến chính mình"
    },
    "totalProcessed": 1,
    "successCount": 0,
    "failedCount": 1
  }
}
```

### 3. Test DELETE `/user/agents/{id}/multi-agents`

#### Test Case 3.1: Gỡ bỏ agent con thành công
```bash
curl -X DELETE "http://localhost:3000/user/agents/550e8400-e29b-41d4-a716-446655440000/multi-agents" \
  -H "Authorization: Bearer {JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "childAgentIds": [
      "550e8400-e29b-41d4-a716-446655440001",
      "550e8400-e29b-41d4-a716-446655440002"
    ]
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "idSuccess": ["550e8400-e29b-41d4-a716-446655440001"],
    "idFailed": ["550e8400-e29b-41d4-a716-446655440002"],
    "errors": {
      "550e8400-e29b-41d4-a716-446655440002": "Quan hệ multi-agent không tồn tại"
    },
    "totalProcessed": 2,
    "successCount": 1,
    "failedCount": 1
  },
  "message": "Gỡ bỏ agent con thành công"
}
```

### 4. Error Test Cases

#### Test Case 4.1: Agent không tồn tại
```bash
curl -X GET "http://localhost:3000/user/agents/00000000-0000-0000-0000-000000000000/multi-agents" \
  -H "Authorization: Bearer {JWT_TOKEN}"
```

**Expected Response:**
```json
{
  "success": false,
  "error": {
    "code": 40101,
    "message": "Không tìm thấy agent"
  }
}
```

#### Test Case 4.2: Validation errors
```bash
curl -X POST "http://localhost:3000/user/agents/550e8400-e29b-41d4-a716-446655440000/multi-agents" \
  -H "Authorization: Bearer {JWT_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "multiAgents": []
  }'
```

**Expected Response:**
```json
{
  "success": false,
  "error": {
    "code": 400,
    "message": "Phải có ít nhất 1 agent con"
  }
}
```

## Performance Tests

### Bulk Operation Test
- Test với 20 agents (maximum limit)
- Test với duplicate agent IDs
- Test với mix của valid và invalid agent IDs

### Search Performance Test
- Test search với large dataset
- Test combined search (search + promptSearch)
- Test pagination với large results
