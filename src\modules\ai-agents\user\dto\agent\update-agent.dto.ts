import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ModelConfigDto } from './create-agent.dto';
import { ProfileDto } from './agent-response.dto';

/**
 * DTO cho việc cập nhật agent
 */
export class UpdateAgentDto {
  /**
   * Tên agent
   */
  @ApiPropertyOptional({
    description: 'Tên agent',
    example: 'Updated Assistant',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  name?: string;

  /**
   * MIME type của avatar
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * <PERSON><PERSON><PERSON> hình model
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> hình model',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsOptional()
  modelConfig?: ModelConfigDto;

  /**
   * Hướng dẫn (instruction)
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn (instruction)',
    example: 'Bạn là trợ lý cá nhân chuyên nghiệp, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  @IsOptional()
  instruction?: string;

  /**
   * Thông tin profile
   */
  @ApiPropertyOptional({
    description: 'Thông tin profile',
    type: ProfileDto,
  })
  @ValidateNested()
  @Type(() => ProfileDto)
  @IsOptional()
  profile?: ProfileDto;

  /**
   * ID của vector store
   */
  @ApiPropertyOptional({
    description: 'ID của vector store',
    example: 'vector-store-1',
  })
  @IsString()
  @IsOptional()
  vectorStoreId?: string;

  /**
   * ID của strategy
   */
  @ApiPropertyOptional({
    description: 'ID của strategy',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsOptional()
  strategyId?: string;

  /**
   * Cấu hình strategy
   */
  @ApiPropertyOptional({
    description: 'Cấu hình strategy',
    example: { key1: 'value1', key2: 'value2' },
  })
  @IsObject()
  @IsOptional()
  strategyConfig?: Record<string, any>;

  /**
   * Danh sách ID của media
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của media',
    example: ['media-1', 'media-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  mediaIds?: string[];

  /**
   * Danh sách ID của sản phẩm
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của sản phẩm',
    example: ['1', '2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  productIds?: string[];

  /**
   * Danh sách ID của URL
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của URL',
    example: ['url-1', 'url-2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  urlIds?: string[];
}
