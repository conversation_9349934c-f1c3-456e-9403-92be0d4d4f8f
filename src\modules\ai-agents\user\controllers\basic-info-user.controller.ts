import {
  Body,
  Controller,
  Get,
  Param,
  Put,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CurrentUser } from '@/modules/auth/decorators';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ErrorCode } from '@common/exceptions';
import { ApiResponseDto } from '@common/response';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { BasicInfoUserService } from '../services/basic-info-user.service';
import {
  UpdateBasicInfoDto,
  BasicInfoResponseDto,
} from '../dto/basic-info';

/**
 * Controller xử lý các API liên quan đến basic info của agent cho người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class BasicInfoUserController {
  constructor(private readonly basicInfoUserService: BasicInfoUserService) { }

  // ==================== BASIC INFO ENDPOINTS ====================

  /**
   * Lấy thông tin basic info của agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @returns Thông tin basic info của agent
   */
  @Get(':id/basic-info')
  @ApiOperation({ summary: 'Lấy thông tin basic info của agent' })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Lấy thông tin basic info thành công',
    type: BasicInfoResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getBasicInfo(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ApiResponseDto<BasicInfoResponseDto>> {
    const result = await this.basicInfoUserService.getBasicInfo(id, userId);
    return ApiResponseDto.success(result, 'Lấy thông tin basic info thành công');
  }

  /**
   * Cập nhật basic info của agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @param updateDto Thông tin basic info cần cập nhật
   * @returns Thông tin basic info đã cập nhật và avatar upload URL (nếu có)
   */
  @Put(':id/basic-info')
  @ApiOperation({ summary: 'Cập nhật basic info của agent' })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật basic info thành công',
    schema: {
      allOf: [
        { $ref: '#/components/schemas/ApiResponseDto' },
        {
          properties: {
            data: {
              allOf: [
                { $ref: '#/components/schemas/BasicInfoResponseDto' },
                {
                  properties: {
                    avatarUpload: {
                      type: 'object',
                      description: 'Thông tin upload avatar (nếu có)',
                      properties: {
                        uploadUrl: {
                          type: 'string',
                          description: 'URL để upload avatar',
                          example: 'https://s3.amazonaws.com/bucket/upload-url'
                        },
                        publicUrl: {
                          type: 'string',
                          description: 'URL public của avatar sau khi upload',
                          example: 'https://s3.amazonaws.com/bucket/avatar.jpg'
                        }
                      }
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    }
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.INVALID_MULTI_AGENT_CONFIG,
    AGENT_ERROR_CODES.INVALID_S3_KEY,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async updateBasicInfo(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateBasicInfoDto,
  ): Promise<ApiResponseDto<BasicInfoResponseDto & { avatarUpload?: { uploadUrl: string; publicUrl: string } }>> {
    const result = await this.basicInfoUserService.updateBasicInfo(id, userId, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật basic info thành công');
  }
}
