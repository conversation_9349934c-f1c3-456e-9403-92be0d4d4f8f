import { apiClient } from '@/shared/api';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';

/**
 * API functions cho Conversion management
 * Tương ứng với ConversionUserController trong backend
 */

export interface ConversionFieldDto {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description?: string;
  required?: boolean;
  options?: string[]; // For enum/select fields
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

export interface ConversionResponseDto {
  fields: ConversionFieldDto[];
  updatedAt: number;
}

export interface UpdateConversionDto {
  fields: ConversionFieldDto[];
}

/**
 * Lấy thông tin conversion config của agent
 * GET /user/agents/{id}/conversion
 */
export const getConversion = async (
  agentId: string
): Promise<ApiResponse<ConversionResponseDto>> => {
  return apiClient.get(`/user/agents/${agentId}/conversion`);
};

/**
 * Cập nhật conversion config của agent
 * PUT /user/agents/{id}/conversion
 */
export const updateConversion = async (
  agentId: string,
  data: UpdateConversionDto
): Promise<ApiResponse<ConversionResponseDto>> => {
  return apiClient.put(`/user/agents/${agentId}/conversion`, data);
};
