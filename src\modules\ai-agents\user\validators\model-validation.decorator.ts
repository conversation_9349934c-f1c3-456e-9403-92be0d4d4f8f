import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

/**
 * Custom validator để kiểm tra logic model:
 * B<PERSON>t buộc có 1 trong 2 khối:
 * - Khối 1: systemModelId
 * - Khối 2: userModelId + keyLlmId
 */
export function IsValidModelConfiguration(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isValidModelConfiguration',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const obj = args.object as any;
          
          // Kiểm tra khối 1: systemModelId
          const hasSystemModel = obj.systemModelId && obj.systemModelId.trim().length > 0;
          
          // Kiểm tra khối 2: userModelId + keyLlmId
          const hasUserModel = obj.userModelId && obj.userModelId.trim().length > 0;
          const hasKeyLlm = obj.keyLlmId && obj.keyLlmId.trim().length > 0;
          const hasUserModelBlock = hasUserModel && hasKeyLlm;
          
          // Phải có ít nhất 1 trong 2 khối
          return hasSystemModel || hasUserModelBlock;
        },
        defaultMessage(args: ValidationArguments) {
          return 'Bắt buộc phải có 1 trong 2 khối: (systemModelId) hoặc (userModelId + keyLlmId)';
        },
      },
    });
  };
}

/**
 * Custom validator để kiểm tra khối user model phải đầy đủ
 */
export function IsCompleteUserModelBlock(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isCompleteUserModelBlock',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const obj = args.object as any;
          
          // Nếu có userModelId thì phải có keyLlmId
          if (obj.userModelId && obj.userModelId.trim().length > 0) {
            return obj.keyLlmId && obj.keyLlmId.trim().length > 0;
          }
          
          // Nếu có keyLlmId thì phải có userModelId
          if (obj.keyLlmId && obj.keyLlmId.trim().length > 0) {
            return obj.userModelId && obj.userModelId.trim().length > 0;
          }
          
          return true; // Nếu không có gì thì OK
        },
        defaultMessage(args: ValidationArguments) {
          return 'Nếu sử dụng user model, phải có đầy đủ userModelId và keyLlmId';
        },
      },
    });
  };
}
