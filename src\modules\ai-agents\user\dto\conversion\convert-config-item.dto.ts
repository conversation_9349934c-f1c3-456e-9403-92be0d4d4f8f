import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsIn,
  IsOptional,
  IsBoolean,
  MaxLength
} from 'class-validator';
import { ConvertConfig } from '@modules/agent/interfaces/convert-config.interface';

/**
 * DTO cho một item trong conversion config - implement ConvertConfig interface
 */
export class ConvertConfigItemDto implements ConvertConfig {
  /**
   * Tên của field trong schema JSON
   */
  @ApiProperty({
    description: 'Tên của field trong schema JSON',
    example: 'customer_name',
    maxLength: 100,
  })
  @IsString({ message: 'Tên field phải là chuỗi' })
  @MaxLength(100, { message: 'Tên field không được vượt quá 100 ký tự' })
  name: string;

  /**
   * Kiểu dữ liệu của field
   */
  @ApiProperty({
    description: '<PERSON>ểu dữ liệu của field',
    example: 'string',
    enum: ['string', 'number', 'boolean', 'array_number', 'array_string', 'enum'],
  })
  @IsIn(['string', 'number', 'boolean', 'array_number', 'array_string', 'enum'], {
    message: 'Kiểu dữ liệu phải là một trong các giá trị: string, number, boolean, array_number, array_string, enum'
  })
  type: 'string' | 'number' | 'boolean' | 'array_number' | 'array_string' | 'enum';

  /**
   * Mô tả (nội dung) của field
   */
  @ApiProperty({
    description: 'Mô tả hoặc nội dung của field',
    example: 'Tên đầy đủ của khách hàng',
    maxLength: 500,
  })
  @IsString({ message: 'Mô tả phải là chuỗi' })
  @MaxLength(500, { message: 'Mô tả không được vượt quá 500 ký tự' })
  description: string;

  /**
   * Trường này có bắt buộc không?
   */
  @ApiProperty({
    description: 'Trường này có bắt buộc không?',
    example: true,
    default: false,
  })
  @IsBoolean({ message: 'Required phải là boolean' })
  @Type(() => Boolean)
  required: boolean;

  /**
   * Giá trị mặc định
   */
  @ApiPropertyOptional({
    description: 'Giá trị mặc định cho field',
    example: 'Khách hàng mới',
  })
  @IsOptional()
  defaultValue?: any;
}
