import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ErrorCode } from '@common/exceptions';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { AgentResourceUserService } from '@modules/agent/user/services';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtUserGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  AddAgentMediaDto,
  AddAgentProductDto,
  AddAgentUrlDto,
  RemoveAgentMediaDto,
  RemoveAgentProductDto,
  RemoveAgentUrlDto,
  AgentMediaQueryDto,
  AgentMediaResponseDto,
  AgentProductQueryDto,
  AgentProductResponseDto,
  AgentUrlQueryDto,
  AgentUrlResponseDto,
  BulkUrlOperationResponseDto,
  BulkMediaOperationResponseDto,
  BulkProductOperationResponseDto,
} from '../dto/resource';

/**
 * Controller xử lý các API endpoint cho tài nguyên của Agent
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  AgentMediaResponseDto,
  AgentUrlResponseDto,
  AgentProductResponseDto,
  ApiResponseDto
)
export class AgentResourceUserController {
  constructor(private readonly agentResourceUserService: AgentResourceUserService) { }

  // ==================== URL ENDPOINTS ====================

  /**
   * Lấy danh sách URL của agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @param queryDto Tham số truy vấn
   * @returns Danh sách URL có phân trang
   */
  @Get(':id/urls')
  @ApiOperation({ summary: 'Lấy danh sách URL của agent' })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách URL thành công',
    schema: ApiResponseDto.getPaginatedSchema(AgentUrlResponseDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getAgentUrls(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
    @Query() queryDto: AgentUrlQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentUrlResponseDto>>> {
    const result = await this.agentResourceUserService.getAgentUrls(id, userId, queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Thêm URL vào agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @param addDto Thông tin URL cần thêm
   * @returns Kết quả bulk operation
   */
  @Post(':id/urls')
  @ApiOperation({ summary: 'Thêm URL vào agent' })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Thêm URL thành công',
    type: BulkUrlOperationResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.URL_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async addAgentUrls(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() addDto: AddAgentUrlDto,
  ): Promise<ApiResponseDto<BulkUrlOperationResponseDto>> {
    const result = await this.agentResourceUserService.addAgentUrls(id, userId, addDto);
    return ApiResponseDto.success(result, 'Thêm URL thành công');
  }

  /**
   * Gỡ bỏ URL khỏi agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @param removeDto Thông tin URL cần gỡ bỏ
   * @returns Kết quả bulk operation
   */
  @Delete(':id/urls')
  @ApiOperation({ summary: 'Gỡ bỏ URL khỏi agent' })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Gỡ bỏ URL thành công',
    type: BulkUrlOperationResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.URL_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async removeAgentUrls(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() removeDto: RemoveAgentUrlDto,
  ): Promise<ApiResponseDto<BulkUrlOperationResponseDto>> {
    const result = await this.agentResourceUserService.removeAgentUrls(id, userId, removeDto);
    return ApiResponseDto.success(result, 'Gỡ bỏ URL thành công');
  }

  // ==================== MEDIA ENDPOINTS ====================

  /**
   * Lấy danh sách Media của agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @param queryDto Tham số truy vấn
   * @returns Danh sách Media có phân trang
   */
  @Get(':id/medias')
  @ApiOperation({ summary: 'Lấy danh sách Media của agent' })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách Media thành công',
    schema: ApiResponseDto.getPaginatedSchema(AgentMediaResponseDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getAgentMedias(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
    @Query() queryDto: AgentMediaQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentMediaResponseDto>>> {
    const result = await this.agentResourceUserService.getAgentMedias(id, userId, queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Thêm Media vào agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @param addDto Thông tin Media cần thêm
   * @returns Kết quả bulk operation
   */
  @Post(':id/medias')
  @ApiOperation({ summary: 'Thêm Media vào agent' })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Thêm Media thành công',
    type: BulkMediaOperationResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.MEDIA_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async addAgentMedias(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() addDto: AddAgentMediaDto,
  ): Promise<ApiResponseDto<BulkMediaOperationResponseDto>> {
    const result = await this.agentResourceUserService.addAgentMedias(id, userId, addDto);
    return ApiResponseDto.success(result, 'Thêm Media thành công');
  }

  /**
   * Gỡ bỏ Media khỏi agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @param removeDto Thông tin Media cần gỡ bỏ
   * @returns Kết quả bulk operation
   */
  @Delete(':id/medias')
  @ApiOperation({ summary: 'Gỡ bỏ Media khỏi agent' })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Gỡ bỏ Media thành công',
    type: BulkMediaOperationResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.MEDIA_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async removeAgentMedias(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() removeDto: RemoveAgentMediaDto,
  ): Promise<ApiResponseDto<BulkMediaOperationResponseDto>> {
    const result = await this.agentResourceUserService.removeAgentMedias(id, userId, removeDto);
    return ApiResponseDto.success(result, 'Gỡ bỏ Media thành công');
  }

  // ==================== PRODUCT ENDPOINTS ====================

  /**
   * Lấy danh sách Product của agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @param queryDto Tham số truy vấn
   * @returns Danh sách Product có phân trang
   */
  @Get(':id/products')
  @ApiOperation({ summary: 'Lấy danh sách Product của agent' })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách Product thành công',
    schema: ApiResponseDto.getPaginatedSchema(AgentProductResponseDto),
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getAgentProducts(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
    @Query() queryDto: AgentProductQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentProductResponseDto>>> {
    const result = await this.agentResourceUserService.getAgentProducts(id, userId, queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Thêm Product vào agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @param addDto Thông tin Product cần thêm
   * @returns Kết quả bulk operation
   */
  @Post(':id/products')
  @ApiOperation({ summary: 'Thêm Product vào agent' })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Thêm Product thành công',
    type: BulkProductOperationResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.PRODUCT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async addAgentProducts(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() addDto: AddAgentProductDto,
  ): Promise<ApiResponseDto<BulkProductOperationResponseDto>> {
    const result = await this.agentResourceUserService.addAgentProducts(id, userId, addDto);
    return ApiResponseDto.success(result, 'Thêm Product thành công');
  }

  /**
   * Gỡ bỏ Product khỏi agent
   * @param userId ID của người dùng
   * @param id ID của agent
   * @param removeDto Thông tin Product cần gỡ bỏ
   * @returns Kết quả bulk operation
   */
  @Delete(':id/products')
  @ApiOperation({ summary: 'Gỡ bỏ Product khỏi agent' })
  @ApiParam({ name: 'id', description: 'ID của agent' })
  @ApiResponse({
    status: 200,
    description: 'Gỡ bỏ Product thành công',
    type: BulkProductOperationResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.PRODUCT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_RESOURCE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async removeAgentProducts(
    @CurrentUser('id') userId: number,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() removeDto: RemoveAgentProductDto,
  ): Promise<ApiResponseDto<BulkProductOperationResponseDto>> {
    const result = await this.agentResourceUserService.removeAgentProducts(id, userId, removeDto);
    return ApiResponseDto.success(result, 'Gỡ bỏ Product thành công');
  }
}
