# Agent Profile APIs

## Tổng Quan

Profile APIs cho phép người dùng quản lý thông tin hồ sơ của agent, bao gồm giớ<PERSON>, ng<PERSON><PERSON>, v<PERSON> tr<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> c<PERSON>, ng<PERSON><PERSON> ngữ và quốc gia.

## Cấu Trúc Database

Profile được lưu trữ dưới dạng JSONB trong bảng `agents_user`:

```sql
-- Trường profile trong bảng agents_user
profile JSONB DEFAULT '{}'
```

## API Endpoints

### 1. Lấy Thông Tin Profile

**GET** `/user/agents/{id}/profile`

<PERSON><PERSON><PERSON> thông tin profile hiện tại của agent.

**Parameters:**
- `id` (path): ID của agent (UUID)

**Response:**
```json
{
  "success": true,
  "data": {
    "gender": "MALE",
    "dateOfBirth": ************,
    "position": "Tr<PERSON> lý AI chuyên về marketing",
    "education": "Th<PERSON><PERSON> sĩ <PERSON> học <PERSON>",
    "skills": [
      "Trả lời câu hỏi",
      "Tìm kiếm thông tin",
      "Phân tích dữ liệu"
    ],
    "personality": [
      "Thân thiện",
      "Kiên nhẫn",
      "Sáng tạo"
    ],
    "languages": [
      "Tiếng Việt",
      "Tiếng Anh",
      "Tiếng Nhật"
    ],
    "nations": "Việt Nam",
    "updatedAt": 1672531200000
  },
  "message": "Lấy thông tin profile thành công"
}
```

### 2. Cập Nhật Profile

**PUT** `/user/agents/{id}/profile`

Cập nhật thông tin profile của agent. Tất cả các trường đều optional.

**Parameters:**
- `id` (path): ID của agent (UUID)

**Request Body:**
```json
{
  "gender": "FEMALE",
  "dateOfBirth": ************,
  "position": "Trợ lý AI chuyên về customer service",
  "education": "Cử nhân Công nghệ Thông tin",
  "skills": [
    "Hỗ trợ khách hàng",
    "Giải quyết vấn đề",
    "Giao tiếp hiệu quả"
  ],
  "personality": [
    "Nhiệt tình",
    "Chuyên nghiệp",
    "Đáng tin cậy"
  ],
  "languages": [
    "Tiếng Việt",
    "Tiếng Anh"
  ],
  "nations": "Việt Nam"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "gender": "FEMALE",
    "dateOfBirth": ************,
    "position": "Trợ lý AI chuyên về customer service",
    "education": "Cử nhân Công nghệ Thông tin",
    "skills": [
      "Hỗ trợ khách hàng",
      "Giải quyết vấn đề",
      "Giao tiếp hiệu quả"
    ],
    "personality": [
      "Nhiệt tình",
      "Chuyên nghiệp",
      "Đáng tin cậy"
    ],
    "languages": [
      "Tiếng Việt",
      "Tiếng Anh"
    ],
    "nations": "Việt Nam",
    "updatedAt": 1672531300000
  },
  "message": "Cập nhật profile thành công"
}
```

## Validation Rules

### **Giới Tính (gender)**
- **Type**: String (enum)
- **Values**: MALE, FEMALE, OTHER
- **Optional**: Có

### **Ngày Sinh (dateOfBirth)**
- **Type**: Number (timestamp millis)
- **Range**: 0 đến hiện tại
- **Optional**: Có
- **Validation**: Không được trong tương lai

### **Vị Trí (position)**
- **Type**: String
- **Max Length**: 255 ký tự
- **Optional**: Có

### **Học Vấn (education)**
- **Type**: String
- **Max Length**: 255 ký tự
- **Optional**: Có

### **Kỹ Năng (skills)**
- **Type**: Array of String
- **Max Items**: 20
- **Optional**: Có

### **Tính Cách (personality)**
- **Type**: Array of String
- **Max Items**: 15
- **Optional**: Có

### **Ngôn Ngữ (languages)**
- **Type**: Array of String
- **Max Items**: 10
- **Optional**: Có

### **Quốc Gia (nations)**
- **Type**: String
- **Max Length**: 100 ký tự
- **Optional**: Có

## Error Codes

- `40101`: Agent không tồn tại hoặc không thuộc về user
- `40120`: Lỗi khi thao tác với tài nguyên agent

## Security

- Tất cả APIs yêu cầu JWT authentication
- User chỉ có thể thao tác với agents thuộc về mình
- Validation nghiêm ngặt cho tất cả input parameters

## Business Logic

### **Partial Update**
- API cập nhật hỗ trợ partial update
- Chỉ các trường được gửi trong request sẽ được cập nhật
- Các trường không gửi sẽ giữ nguyên giá trị cũ

### **Data Persistence**
- Profile được lưu dưới dạng JSONB trong PostgreSQL
- Tự động cập nhật `updatedAt` timestamp khi có thay đổi
- Hỗ trợ query và index trên các trường JSONB

### **Type Safety**
- Sử dụng TypeScript interfaces cho type safety
- Validation đầy đủ với class-validator
- Mapper pattern để chuyển đổi giữa DTO và Entity
