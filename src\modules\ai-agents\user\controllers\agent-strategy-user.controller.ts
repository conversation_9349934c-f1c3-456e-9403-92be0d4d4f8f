import { ApiResponseDto } from '@common/response';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { AgentStrategyService } from '@modules/agent/user/services/agent-strategy.service';
import { JwtUserGuard } from '@modules/auth/guards';
import {
  Controller,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiTags
} from '@nestjs/swagger';
/**
 * Controller xử lý các API endpoint cho Strategy của Agent người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('user/agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto
)
export class AgentStrategyUserController {
  constructor(private readonly agentStrategyService: AgentStrategyService) { }

  // /**
  //  * Lấy thông tin strategy của agent
  //  * @param userId ID của người dùng
  //  * @param id ID của agent
  //  * @returns Thông tin strategy của agent
  //  */
  // @Get(':id/strategy')
  // @ApiOperation({ summary: 'Lấy thông tin strategy của agent' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Lấy thông tin strategy thành công',
  //   schema: ApiResponseDto.getSchema(AgentStrategyDto),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_NOT_FOUND,
  //   AGENT_ERROR_CODES.AGENT_FETCH_FAILED,
  //   STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND,
  //   STRATEGY_ERROR_CODES.STRATEGY_VERSION_NOT_FOUND,
  //   STRATEGY_ERROR_CODES.STRATEGY_NOT_ASSIGNED,
  //   STRATEGY_ERROR_CODES.STRATEGY_NO_VERSIONS,
  //   STRATEGY_ERROR_CODES.STRATEGY_FETCH_FAILED,
  //   ErrorCode.INTERNAL_SERVER_ERROR
  // )
  // async getAgentStrategy(
  //   @CurrentUser('id') userId: number,
  //   @Param('id') id: string,
  //   @Query() query: GetAgentStrategyDto,
  // ) {
  //   const result = await this.agentStrategyService.getAgentStrategy(id, userId, query.versionId);
  //   return ApiResponseDto.success(result, 'Lấy thông tin strategy thành công');
  // }

  // /**
  //  * Gán strategy cho agent
  //  * @param userId ID của người dùng
  //  * @param id ID của agent
  //  * @param dto Thông tin strategy
  //  * @returns Thông báo thành công
  //  */
  // @Patch(':id/strategy')
  // @ApiOperation({ summary: 'Gán strategy cho agent' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Gán strategy thành công',
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_NOT_FOUND,
  //   STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND,
  //   STRATEGY_ERROR_CODES.STRATEGY_VERSION_NOT_FOUND,
  //   STRATEGY_ERROR_CODES.STRATEGY_NO_VERSIONS,
  //   STRATEGY_ERROR_CODES.STRATEGY_ACCESS_DENIED,
  //   STRATEGY_ERROR_CODES.STRATEGY_ASSIGN_FAILED,
  //   STRATEGY_ERROR_CODES.STRATEGY_FETCH_FAILED,
  //   ErrorCode.INTERNAL_SERVER_ERROR
  // )
  // async assignStrategyToAgent(
  //   @CurrentUser('id') userId: number,
  //   @Param('id') id: string,
  //   @Body() dto: AssignStrategyToAgentDto,
  // ) {
  //   await this.agentStrategyService.assignStrategyToAgent(id, userId, dto);
  //   return ApiResponseDto.success(null, 'Gán strategy thành công');
  // }
}
