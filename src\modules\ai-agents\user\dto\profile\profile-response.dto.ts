import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho response thông tin profile của agent
 */
export class ProfileResponseDto {
  /**
   * Giới tính
   */
  @ApiPropertyOptional({
    description: 'Giới tính của agent',
    example: 'MALE',
  })
  gender?: string;

  /**
   * <PERSON><PERSON><PERSON> (timestamp millis)
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> sinh (timestamp millis)',
    example: 946684800000,
  })
  dateOfBirth?: number;

  /**
   * Vị trí/Chức vụ
   */
  @ApiPropertyOptional({
    description: 'Vị trí hoặc chức vụ của agent',
    example: 'Trợ lý AI chuyên về marketing',
  })
  position?: string;

  /**
   * Trình độ học vấn
   */
  @ApiPropertyOptional({
    description: 'Trình độ học vấn của agent',
    example: 'Thạc sĩ <PERSON> học <PERSON>h',
  })
  education?: string;

  /**
   * Danh sách kỹ năng
   */
  @ApiPropertyOptional({
    description: '<PERSON>h sách kỹ năng của agent',
    example: ['Trả lời câu hỏi', 'Tìm kiếm thông tin', 'Phân tích dữ liệu'],
    type: [String],
  })
  skills?: string[];

  /**
   * Danh sách tính cách
   */
  @ApiPropertyOptional({
    description: 'Danh sách tính cách của agent',
    example: ['Thân thiện', 'Kiên nhẫn', 'Sáng tạo'],
    type: [String],
  })
  personality?: string[];

  /**
   * Danh sách ngôn ngữ
   */
  @ApiPropertyOptional({
    description: 'Danh sách ngôn ngữ mà agent có thể sử dụng',
    example: ['Tiếng Việt', 'Tiếng Anh', 'Tiếng Nhật'],
    type: [String],
  })
  languages?: string[];

  /**
   * Quốc gia/Khu vực
   */
  @ApiPropertyOptional({
    description: 'Quốc gia hoặc khu vực mà agent phục vụ',
    example: 'Việt Nam',
  })
  nations?: string;

  /**
   * Thời điểm cập nhật profile gần nhất (timestamp millis)
   */
  @ApiProperty({
    description: 'Thời điểm cập nhật profile gần nhất (timestamp millis)',
    example: 1672531200000,
  })
  updatedAt: number;
}
