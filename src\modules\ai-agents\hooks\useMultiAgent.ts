import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import {
  getMultiAgentRelations,
  createMultiAgentRelation,
  updateMultiAgentRelation,
  deleteMultiAgentRelation,
  bulkUpdateMultiAgentRelations,
  MultiAgentResponseDto,
  CreateMultiAgentRelationDto,
  UpdateMultiAgentRelationDto,
  BulkUpdateMultiAgentDto
} from '../api/multi-agent.api';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';

/**
 * Hook để lấy danh sách quan hệ multi-agent
 * @param agentId ID của agent
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetMultiAgentRelations = (
  agentId: string | undefined,
  options?: UseQueryOptions<ApiResponse<MultiAgentResponseDto>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.MULTI_AGENT, agentId],
    queryFn: () => getMultiAgentRelations(agentId as string),
    enabled: !!agentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để tạo quan hệ multi-agent mới
 * @returns Mutation result
 */
export const useCreateMultiAgentRelation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.CREATE_MULTI_AGENT_RELATION],
    mutationFn: ({ agentId, data }: { agentId: string; data: CreateMultiAgentRelationDto }) =>
      createMultiAgentRelation(agentId, data),
    onSuccess: (_, { agentId }) => {
      // Invalidate multi-agent relations
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.MULTI_AGENT, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, agentId] });
    },
  });
};

/**
 * Hook để cập nhật quan hệ multi-agent
 * @returns Mutation result
 */
export const useUpdateMultiAgentRelation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.UPDATE_MULTI_AGENT_RELATION],
    mutationFn: ({ 
      parentAgentId, 
      childAgentId, 
      data 
    }: { 
      parentAgentId: string; 
      childAgentId: string; 
      data: UpdateMultiAgentRelationDto 
    }) => updateMultiAgentRelation(parentAgentId, childAgentId, data),
    onSuccess: (_, { parentAgentId }) => {
      // Invalidate multi-agent relations
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.MULTI_AGENT, parentAgentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, parentAgentId] });
    },
  });
};

/**
 * Hook để xóa quan hệ multi-agent
 * @returns Mutation result
 */
export const useDeleteMultiAgentRelation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.DELETE_MULTI_AGENT_RELATION],
    mutationFn: ({ parentAgentId, childAgentId }: { parentAgentId: string; childAgentId: string }) =>
      deleteMultiAgentRelation(parentAgentId, childAgentId),
    onSuccess: (_, { parentAgentId }) => {
      // Invalidate multi-agent relations
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.MULTI_AGENT, parentAgentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, parentAgentId] });
    },
  });
};

/**
 * Hook để cập nhật hàng loạt quan hệ multi-agent
 * @returns Mutation result
 */
export const useBulkUpdateMultiAgentRelations = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.BULK_UPDATE_MULTI_AGENT],
    mutationFn: ({ agentId, data }: { agentId: string; data: BulkUpdateMultiAgentDto }) =>
      bulkUpdateMultiAgentRelations(agentId, data),
    onSuccess: (_, { agentId }) => {
      // Invalidate multi-agent relations
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.MULTI_AGENT, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, agentId] });
    },
  });
};
