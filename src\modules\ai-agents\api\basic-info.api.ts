import { apiClient } from '@/shared/api';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';

/**
 * API functions cho Basic Info management
 * Tương ứng với BasicInfoUserController trong backend
 */

export interface BasicInfoResponseDto {
  avatar: string | null;
  name: string;
  provider: string;
  keyLlm?: string;
  modelId: string;
  modelConfig: {
    temperature: number;
    topP: number;
    topK: number;
    maxTokens: number;
  };
  instruction?: string;
  vectorId?: string;
  avatarUpload?: {
    uploadUrl: string;
    publicUrl: string;
  };
  updatedAt: number;
}

export interface UpdateBasicInfoDto {
  name?: string;
  avatarFile?: {
    fileName: string;
    mimeType: string;
  };
  modelConfig?: {
    temperature?: number;
    topP?: number;
    topK?: number;
    maxTokens?: number;
  };
  instruction?: string;
  vectorId?: string;
  // Model selection - chỉ một trong các options sau
  userModelId?: string;
  keyLlmId?: string;
  systemModelId?: string;
  modelFineTuneId?: string;
}

/**
 * Lấy thông tin basic info của agent
 * GET /user/agents/{id}/basic-info
 */
export const getBasicInfo = async (
  agentId: string
): Promise<ApiResponse<BasicInfoResponseDto>> => {
  return apiClient.get(`/user/agents/${agentId}/basic-info`);
};

/**
 * Cập nhật basic info của agent
 * PUT /user/agents/{id}/basic-info
 */
export const updateBasicInfo = async (
  agentId: string,
  data: UpdateBasicInfoDto
): Promise<ApiResponse<BasicInfoResponseDto & { avatarUpload?: { uploadUrl: string; publicUrl: string } }>> => {
  return apiClient.put(`/user/agents/${agentId}/basic-info`, data);
};
