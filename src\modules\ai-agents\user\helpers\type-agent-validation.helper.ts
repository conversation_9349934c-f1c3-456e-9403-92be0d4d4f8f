import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { TypeAgentRepository } from '@modules/agent/repositories';
import { TypeAgentConfig } from '@modules/agent/interfaces/type-agent-config.interface';
import { CreateAgentDto } from '../dto/agent/create-agent.dto';

/**
 * Helper class để validation TypeAgent configuration cho agent creation
 */
@Injectable()
export class TypeAgentValidationHelper {
  private readonly logger = new Logger(TypeAgentValidationHelper.name);

  constructor(
    private readonly typeAgentRepository: TypeAgentRepository,
  ) {}

  /**
   * Validate agent creation data dựa trên TypeAgent configuration
   * @param createDto DTO tạo agent
   * @returns TypeAgent configuration
   */
  async validateAgentCreation(createDto: CreateAgentDto): Promise<TypeAgentConfig> {
    // Lấy thông tin TypeAgent
    const typeAgent = await this.typeAgentRepository.findById(createDto.typeId);
    if (!typeAgent) {
      throw new AppException(
        AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
        `Type agent với ID ${createDto.typeId} không tồn tại`
      );
    }

    const config = typeAgent.config;

    // Validate từng khối dựa trên configuration
    await this.validateProfile(config, createDto);
    await this.validateOutputWebsite(config, createDto);
    await this.validateOutputMessenger(config, createDto);
    await this.validateStrategy(config, createDto);
    await this.validateResources(config, createDto);
    await this.validateMultiAgent(config, createDto);
    await this.validateConvert(config, createDto);

    return config;
  }

    /**
   * Validate profile block
   * @param config TypeAgent configuration
   * @param createDto DTO tạo agent
   */
  private async validateConvert(config: TypeAgentConfig, createDto: CreateAgentDto): Promise<void> {
    // Nếu TypeAgent không hỗ trợ profile nhưng có dữ liệu profile được gửi lên
    if (!config.enableTaskConversionTracking && createDto.conversion) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CONVERT_NOT_SUPPORTED,
        'Convert không được hỗ trợ cho loại agent này'
      );
    }

    // Nếu TypeAgent hỗ trợ convert, cho phép convert null/empty
    // Không cần validate bắt buộc
  }

  /**
   * Validate profile block
   * @param config TypeAgent configuration
   * @param createDto DTO tạo agent
   */
  private async validateProfile(config: TypeAgentConfig, createDto: CreateAgentDto): Promise<void> {
    // Nếu TypeAgent không hỗ trợ profile nhưng có dữ liệu profile được gửi lên
    if (!config.enableAgentProfileCustomization && createDto.profile) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_PROFILE_NOT_SUPPORTED,
        'Profile không được hỗ trợ cho loại agent này'
      );
    }

    // Nếu TypeAgent hỗ trợ profile, cho phép profile null/empty
    // Không cần validate bắt buộc
  }

  /**
   * Validate output block
   * @param config TypeAgent configuration
   * @param createDto DTO tạo agent
   */
  private async validateOutputMessenger(config: TypeAgentConfig, createDto: CreateAgentDto): Promise<void> {
    // Nếu TypeAgent không hỗ trợ output nhưng có dữ liệu output được gửi lên
    if (!config.enableOutputToMessenger && createDto.outputMessenger) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
        'Output không được hỗ trợ cho loại agent này'
      );
    }

    // Nếu TypeAgent hỗ trợ output, cho phép output null/empty
    // Không cần validate bắt buộc phải có Facebook Pages hoặc Websites
  }

  private async validateOutputWebsite(config: TypeAgentConfig, createDto: CreateAgentDto): Promise<void> {
    // Nếu TypeAgent không hỗ trợ output nhưng có dữ liệu output được gửi lên
    if (!config.enableOutputToWebsiteLiveChat && createDto.outputWebsite) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
        'Output không được hỗ trợ cho loại agent này'
      );
    }


    // Nếu TypeAgent hỗ trợ output, cho phép output null/empty
    // Không cần validate bắt buộc phải có Facebook Pages hoặc Websites
  }

  /**
   * Validate strategy block
   * @param config TypeAgent configuration
   * @param createDto DTO tạo agent
   */
  private async validateStrategy(config: TypeAgentConfig, createDto: CreateAgentDto): Promise<void> {
    // Nếu TypeAgent không hỗ trợ strategy nhưng có dữ liệu strategy được gửi lên
    if (!config.enableDynamicStrategyExecution && createDto.strategy) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_STRATEGY_NOT_SUPPORTED,
        'Strategy không được hỗ trợ cho loại agent này'
      );
    }

    // Nếu TypeAgent hỗ trợ strategy, cho phép strategy null/empty
    // Không cần validate bắt buộc phải có strategyId
  }

  /**
   * Validate resources block
   * @param config TypeAgent configuration
   * @param createDto DTO tạo agent
   */
  private async validateResources(config: TypeAgentConfig, createDto: CreateAgentDto): Promise<void> {
    // Nếu TypeAgent không hỗ trợ resources nhưng có dữ liệu resources được gửi lên
    if (!config.enableResourceUsage && createDto.resources) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_RESOURCES_NOT_SUPPORTED,
        'Resources không được hỗ trợ cho loại agent này'
      );
    }

    // Nếu TypeAgent hỗ trợ resources, cho phép resources null/empty
    // Không cần validate bắt buộc phải có Media, URL hoặc Product
  }

  /**
   * Validate multi agent block
   * @param config TypeAgent configuration
   * @param createDto DTO tạo agent
   */
  private async validateMultiAgent(config: TypeAgentConfig, createDto: CreateAgentDto): Promise<void> {
    // Nếu TypeAgent không hỗ trợ multiAgent nhưng có dữ liệu multiAgent được gửi lên
    if (!config.enableMultiAgentCollaboration && createDto.multiAgent) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_MULTI_AGENT_NOT_SUPPORTED,
        'Multi Agent không được hỗ trợ cho loại agent này'
      );
    }

    // Nếu TypeAgent hỗ trợ multiAgent và có dữ liệu, validate cấu trúc
    if (config.enableMultiAgentCollaboration && createDto.multiAgent && createDto.multiAgent.multiAgent && createDto.multiAgent.multiAgent.length > 0) {
      const agents = createDto.multiAgent.multiAgent;

      // Kiểm tra không có agent ID trùng lặp
      const agentIds = agents.map(agent => agent.agent_id);
      const uniqueAgentIds = new Set(agentIds);
      if (agentIds.length !== uniqueAgentIds.size) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_MULTI_AGENT_DUPLICATE,
          'Không được có agent ID trùng lặp trong Multi Agent'
        );
      }

      // Validate từng agent có prompt khi có dữ liệu
      for (const agent of agents) {
        if (!agent.prompt || agent.prompt.trim().length === 0) {
          throw new AppException(
            AGENT_ERROR_CODES.AGENT_MULTI_AGENT_PROMPT_REQUIRED,
            `Agent ${agent.agent_id} phải có prompt`
          );
        }
      }
    }
  }
}
