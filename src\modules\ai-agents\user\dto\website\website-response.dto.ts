import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin Website trong Agent
 */
export class AgentWebsiteDto {
  /**
   * UUID của Website trong hệ thống
   */
  @ApiProperty({
    description: 'UUID của Website trong hệ thống',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  id: string;

  /**
   * Tên website do người dùng đặt
   */
  @ApiProperty({
    description: 'Tên website do người dùng đặt',
    example: 'Website của tôi'
  })
  websiteName: string;

  /**
   * Tên miền hoặc địa chỉ host của website
   */
  @ApiProperty({
    description: 'Tên miền hoặc địa chỉ host của website',
    example: 'example.com'
  })
  host: string;

  /**
   * Trạng thái xác minh của website
   */
  @ApiProperty({
    description: 'Trạng thái xác minh của website',
    example: true
  })
  verify: boolean;

  /**
   * Trạng thái hoạt động (đ<PERSON><PERSON>c tính từ việc có agentId hay không)
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động của Website trong Agent',
    example: true
  })
  isActive: boolean;
}

/**
 * DTO cho danh sách Website trong Agent
 */
export class AgentWebsiteListDto {
  /**
   * Danh sách Website
   */
  @ApiProperty({
    description: 'Danh sách Website trong Agent',
    type: [AgentWebsiteDto]
  })
  websites: AgentWebsiteDto[];
}
