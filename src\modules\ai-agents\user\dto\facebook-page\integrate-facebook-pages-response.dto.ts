import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response của việc tích hợp danh sách Facebook Pages
 */
export class IntegrateFacebookPagesResponseDto {
  /**
   * Thông báo kết quả
   */
  @ApiProperty({
    description: 'Thông báo kết quả tích hợp',
    example: '<PERSON><PERSON><PERSON> hợp danh sách Facebook Page thành công'
  })
  message: string;

  /**
   * Số lượng Facebook Page đã tích hợp thành công
   */
  @ApiProperty({
    description: 'Số lượng Facebook Page đã tích hợp thành công',
    example: 2
  })
  integratedCount: number;

  /**
   * Số lượng Facebook Page bị bỏ qua (đã tích hợp trước đó)
   */
  @ApiProperty({
    description: 'Số lượng Facebook Page bị bỏ qua (đã tích hợp trước đó)',
    example: 0
  })
  skippedCount: number;

  /**
   * Chi tiết kết quả cho từng Facebook Page
   */
  @ApiProperty({
    description: 'Chi tiết kết quả cho từng Facebook Page',
    example: [
      { facebookPageId: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890', status: 'integrated' },
      { facebookPageId: 'b2c3d4e5-f6g7-8901-bcde-f23456789012', status: 'integrated' }
    ],
    type: [Object]
  })
  details: Array<{
    facebookPageId: string;
    status: 'integrated' | 'skipped' | 'error';
    error?: string;
  }>;
}
