import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsPositive, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc gán strategy cho agent
 */
export class AssignStrategyToAgentDto {
  /**
   * ID của strategy
   */
  @ApiProperty({
    description: 'ID của strategy',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  strategyId?: string;

  /**
   * ID của phiên bản strategy (nếu không cung cấp, sẽ sử dụng phiên bản mặc định)
   */
  @ApiPropertyOptional({
    description: 'ID của phiên bản strategy (nếu không cung cấp, sẽ sử dụng phiên bản mặc định)',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  @Type(() => Number)
  versionId?: number;
}
