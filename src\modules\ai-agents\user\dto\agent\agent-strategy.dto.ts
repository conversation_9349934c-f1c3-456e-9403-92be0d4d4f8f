import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho thông tin bước trong strategy
 */
export class StrategyStepDto {
  /**
   * ID của bước
   */
  @ApiProperty({
    description: 'ID của bước',
    example: 1,
  })
  @IsNumber()
  id: number;

  /**
   * Thứ tự của bước
   */
  @ApiProperty({
    description: 'Thứ tự của bước',
    example: 1,
  })
  @IsNumber()
  stepOrder: number;

  /**
   * Nội dung có thể chỉnh sửa của bước
   */
  @ApiProperty({
    description: 'Nội dung có thể chỉnh sửa của bước',
    example: 'Nội dung mẫu cho bước này',
  })
  @IsString()
  editableExample: string;

  /**
   * C<PERSON> đánh dấu bước có được chỉnh sửa hay không
   */
  @ApiProperty({
    description: 'C<PERSON> đánh dấu bước có được chỉnh sửa hay không',
    example: false,
  })
  @IsBoolean()
  edited: boolean;
}

/**
 * DTO cho thông tin strategy trong agent
 */
export class AgentStrategyDto {
  /**
   * ID của strategy
   */
  @ApiProperty({
    description: 'ID của strategy',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  /**
   * Tên của strategy
   */
  @ApiProperty({
    description: 'Tên của strategy',
    example: 'Customer Support Strategy',
  })
  @IsString()
  name: string;

  /**
   * Mô tả của strategy
   */
  @ApiPropertyOptional({
    description: 'Mô tả của strategy',
    example: 'Chiến lược hỗ trợ khách hàng',
  })
  @IsString()
  @IsOptional()
  description?: string;

  /**
   * ID của phiên bản strategy
   */
  @ApiPropertyOptional({
    description: 'ID của phiên bản strategy',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  versionId?: number;

  /**
   * Tên của phiên bản strategy
   */
  @ApiPropertyOptional({
    description: 'Tên của phiên bản strategy',
    example: 'v1.0',
  })
  @IsString()
  @IsOptional()
  versionName?: string;

  /**
   * Danh sách các bước của strategy
   */
  @ApiPropertyOptional({
    description: 'Danh sách các bước của strategy',
    type: [StrategyStepDto],
  })
  @IsArray()
  @IsOptional()
  @Type(() => StrategyStepDto)
  steps?: StrategyStepDto[];
}

/**
 * DTO cho việc cập nhật strategy cho agent
 */
export class UpdateAgentStrategyDto {
  /**
   * ID của strategy
   */
  @ApiProperty({
    description: 'ID của strategy',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  strategyId: string;

  /**
   * Cấu hình của strategy
   */
  @ApiPropertyOptional({
    description: 'Cấu hình của strategy',
    example: { key1: 'value1', key2: 'value2' },
  })
  @IsOptional()
  config?: Record<string, any>;
}
