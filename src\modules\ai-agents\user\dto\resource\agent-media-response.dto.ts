import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin media của agent
 */
export class AgentMediaResponseDto {
  /**
   * ID của media
   */
  @ApiProperty({
    description: 'ID của media',
    example: 'm1e2d3i4a5-1',
  })
  id: string;

  /**
   * Tên file
   */
  @ApiProperty({
    description: 'Tên file',
    example: 'product-image.jpg',
  })
  name: string;

  /**
   * URL của file
   */
  @ApiProperty({
    description: 'URL của file',
    example: 'https://cdn.example.com/medias/product-image.jpg',
  })
  url: string;

  /**
   * Loại media
   */
  @ApiProperty({
    description: 'Loại media (MIME type) ',
    example: 'image/jpeg',
  })
  description?: string;

  /**
   * <PERSON><PERSON>ch thước file (bytes)
   */
  @ApiProperty({
    description: 'Kích thước file (bytes)',
    example: 1024000,
  })
  size: number;

  /**
   * Thời điểm tạo (timestamp millis)
   */
  @ApiProperty({
    description: 'Thời điểm tạo (timestamp millis)',
    example: 1672531200000,
  })
  createdAt: number;
}
