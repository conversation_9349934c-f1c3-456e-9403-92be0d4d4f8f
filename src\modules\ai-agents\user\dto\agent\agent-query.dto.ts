import { QueryDto, SortDirection } from '@common/dto/query.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsOptional } from 'class-validator';

/**
 * Enum cho các trường sắp xếp của agent
 */
export enum AgentSortBy {
  NAME = 'name',
  CREATED_AT = 'createdAt',
}

/**
 * DTO cho việc truy vấn danh sách agent
 */
export class AgentQueryDto extends QueryDto {

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: AgentSortBy,
    default: AgentSortBy.CREATED_AT,
  })
  @IsEnum(AgentSortBy)
  @IsOptional()
  sortBy: AgentSortBy = AgentSortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
  })
  @IsEnum(SortDirection)
  @IsOptional()
  @Type(() => String)
  sortDirection: SortDirection = SortDirection.DESC;
}
