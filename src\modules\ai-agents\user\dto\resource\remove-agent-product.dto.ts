import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNumber, ArrayMinSize, ArrayMaxSize, ArrayUnique, IsPositive } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc gỡ bỏ sản phẩm khỏi agent
 */
export class RemoveAgentProductDto {
  /**
   * Danh sách ID của sản phẩm cần gỡ bỏ
   */
  @ApiProperty({
    description: 'Danh sách ID của sản phẩm cần gỡ bỏ (tối thiểu 1, tối đa 100)',
    example: [1, 2, 3],
    type: [Number],
    minItems: 1,
    maxItems: 100,
  })
  @IsArray({ message: 'productIds phải là một mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất 1 Product' })
  @ArrayMaxSize(100, { message: 'Không được vượt quá 100 Product' })
  @ArrayUnique({ message: 'Không được có Product ID trùng lặp' })
  @Type(() => Number)
  @IsNumber({}, { each: true, message: 'Mỗi Product ID phải là số' })
  @IsPositive({ each: true, message: 'Mỗi Product ID phải là số dương' })
  productIds: number[];
}
