import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import { getConversion, updateConversion, ConversionResponseDto, UpdateConversionDto } from '../api/conversion.api';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';

/**
 * Hook để lấy thông tin conversion config của agent
 * @param agentId ID của agent
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetConversion = (
  agentId: string | undefined,
  options?: UseQueryOptions<ApiResponse<ConversionResponseDto>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.CONVERSION, agentId],
    queryFn: () => getConversion(agentId as string),
    enabled: !!agentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để cập nhật conversion config của agent
 * @returns Mutation result
 */
export const useUpdateConversion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.UPDATE_CONVERSION],
    mutationFn: ({ agentId, data }: { agentId: string; data: UpdateConversionDto }) =>
      updateConversion(agentId, data),
    onSuccess: (_, { agentId }) => {
      // Invalidate conversion và agent detail
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.CONVERSION, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, agentId] });
    },
  });
};
