import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';
import { getBasicInfo, updateBasicInfo, BasicInfoResponseDto, UpdateBasicInfoDto } from '../api/basic-info.api';
import { AGENT_QUERY_KEYS } from '../constants/agent-query-keys';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';

/**
 * Hook để lấy thông tin basic info của agent
 * @param agentId ID của agent
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetBasicInfo = (
  agentId: string | undefined,
  options?: UseQueryOptions<ApiResponse<BasicInfoResponseDto>>
) => {
  return useQuery({
    queryKey: [AGENT_QUERY_KEYS.BASIC_INFO, agentId],
    queryFn: () => getBasicInfo(agentId as string),
    enabled: !!agentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để cập nhật basic info của agent
 * @returns Mutation result
 */
export const useUpdateBasicInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: [AGENT_QUERY_KEYS.UPDATE_BASIC_INFO],
    mutationFn: ({ agentId, data }: { agentId: string; data: UpdateBasicInfoDto }) =>
      updateBasicInfo(agentId, data),
    onSuccess: (_, { agentId }) => {
      // Invalidate basic info và agent detail
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.BASIC_INFO, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_DETAIL, agentId] });
      queryClient.invalidateQueries({ queryKey: [AGENT_QUERY_KEYS.AGENT_LIST] });
    },
  });
};
