import { apiClient } from '@/shared/api';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * API functions cho Resource management
 * Tương ứng với AgentResourceUserController trong backend
 */

export interface AgentMediaDto {
  id: string;
  fileName: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  createdAt: number;
}

export interface AgentUrlDto {
  id: string;
  url: string;
  title?: string;
  description?: string;
  createdAt: number;
}

export interface AgentProductDto {
  id: string;
  productId: string;
  productName: string;
  productDescription?: string;
  price?: number;
  currency?: string;
  createdAt: number;
}

export interface ResourceResponseDto {
  media: AgentMediaDto[];
  urls: AgentUrlDto[];
  products: AgentProductDto[];
  totalMedia: number;
  totalUrls: number;
  totalProducts: number;
  updatedAt: number;
}

export interface CreateMediaDto {
  fileName: string;
  mimeType: string;
  size: number;
}

export interface CreateUrlDto {
  url: string;
  title?: string;
  description?: string;
}

export interface CreateProductDto {
  productId: string;
}

export interface ResourceQueryDto extends QueryDto {
  type?: 'media' | 'url' | 'product';
  search?: string;
}

/**
 * Lấy danh sách resources của agent
 * GET /user/agents/{id}/resources
 */
export const getResources = async (
  agentId: string,
  params?: ResourceQueryDto
): Promise<ApiResponse<ResourceResponseDto>> => {
  return apiClient.get(`/user/agents/${agentId}/resources`, { params });
};

/**
 * Lấy danh sách media với phân trang
 * GET /user/agents/{id}/resources/media
 */
export const getAgentMedia = async (
  agentId: string,
  params?: QueryDto
): Promise<ApiResponse<PaginatedResult<AgentMediaDto>>> => {
  return apiClient.get(`/user/agents/${agentId}/resources/media`, { params });
};

/**
 * Lấy danh sách URLs với phân trang
 * GET /user/agents/{id}/resources/urls
 */
export const getAgentUrls = async (
  agentId: string,
  params?: QueryDto
): Promise<ApiResponse<PaginatedResult<AgentUrlDto>>> => {
  return apiClient.get(`/user/agents/${agentId}/resources/urls`, { params });
};

/**
 * Lấy danh sách products với phân trang
 * GET /user/agents/{id}/resources/products
 */
export const getAgentProducts = async (
  agentId: string,
  params?: QueryDto
): Promise<ApiResponse<PaginatedResult<AgentProductDto>>> => {
  return apiClient.get(`/user/agents/${agentId}/resources/products`, { params });
};

/**
 * Thêm media vào agent
 * POST /user/agents/{id}/resources/media
 */
export const addAgentMedia = async (
  agentId: string,
  data: CreateMediaDto
): Promise<ApiResponse<AgentMediaDto & { uploadUrl: string; publicUrl: string }>> => {
  return apiClient.post(`/user/agents/${agentId}/resources/media`, data);
};

/**
 * Thêm URL vào agent
 * POST /user/agents/{id}/resources/urls
 */
export const addAgentUrl = async (
  agentId: string,
  data: CreateUrlDto
): Promise<ApiResponse<AgentUrlDto>> => {
  return apiClient.post(`/user/agents/${agentId}/resources/urls`, data);
};

/**
 * Thêm product vào agent
 * POST /user/agents/{id}/resources/products
 */
export const addAgentProduct = async (
  agentId: string,
  data: CreateProductDto
): Promise<ApiResponse<AgentProductDto>> => {
  return apiClient.post(`/user/agents/${agentId}/resources/products`, data);
};

/**
 * Xóa media khỏi agent
 * DELETE /user/agents/{agentId}/resources/media/{mediaId}
 */
export const removeAgentMedia = async (
  agentId: string,
  mediaId: string
): Promise<ApiResponse<void>> => {
  return apiClient.delete(`/user/agents/${agentId}/resources/media/${mediaId}`);
};

/**
 * Xóa URL khỏi agent
 * DELETE /user/agents/{agentId}/resources/urls/{urlId}
 */
export const removeAgentUrl = async (
  agentId: string,
  urlId: string
): Promise<ApiResponse<void>> => {
  return apiClient.delete(`/user/agents/${agentId}/resources/urls/${urlId}`);
};

/**
 * Xóa product khỏi agent
 * DELETE /user/agents/{agentId}/resources/products/{productId}
 */
export const removeAgentProduct = async (
  agentId: string,
  productId: string
): Promise<ApiResponse<void>> => {
  return apiClient.delete(`/user/agents/${agentId}/resources/products/${productId}`);
};
