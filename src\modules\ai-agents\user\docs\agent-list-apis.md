# Agent List APIs

## Tổng Quan

Agent List APIs cung cấp 2 endpoints để lấy danh sách agents của người dùng với các mức độ thông tin khác nhau:

1. **Simple List**: <PERSON><PERSON> sách đơn giản chỉ có id, name, avatar
2. **Full List**: <PERSON><PERSON> sách đầy đủ với thông tin chi tiết

## API Endpoints

### 1. <PERSON><PERSON><PERSON><PERSON>ách Agent Đơ<PERSON>

**GET** `/user/agents/simple`

Lấy danh sách agent đơn giản với thông tin cơ bản nhất.

**Query Parameters:**
- `page` (optional): <PERSON><PERSON> trang (default: 1)
- `limit` (optional): Số items per page (default: 10)
- `search` (optional): Từ khóa tìm kiếm theo tên
- `sortBy` (optional): Trường sắp xếp ('name' | 'createdAt', default: 'createdAt')
- `sortDirection` (optional): <PERSON><PERSON><PERSON><PERSON> sắp xếp ('ASC' | 'DESC', default: 'DESC')

**Response:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "agent-uuid-123",
        "name": "AI Assistant Marketing",
        "avatar": "https://cdn.example.com/avatars/agent-avatar.jpg"
      }
    ],
    "meta": {
      "totalItems": 25,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 3,
      "currentPage": 1
    }
  },
  "message": "Lấy danh sách agent đơn giản thành công"
}
```

### 2. Lấy Danh Sách Agent Đầy Đủ

**GET** `/user/agents`

Lấy danh sách agent với thông tin đầy đủ bao gồm type, exp, rank, model info.

**Query Parameters:**
- `page` (optional): Số trang (default: 1)
- `limit` (optional): Số items per page (default: 10)
- `search` (optional): Từ khóa tìm kiếm theo tên
- `typeId` (optional): Lọc theo ID loại agent
- `sortBy` (optional): Trường sắp xếp ('name' | 'createdAt', default: 'createdAt')
- `sortDirection` (optional): Hướng sắp xếp ('ASC' | 'DESC', default: 'DESC')

**Response:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "agent-uuid-123",
        "name": "AI Assistant Marketing",
        "avatar": "https://cdn.example.com/avatars/agent-avatar.jpg",
        "typeId": 1,
        "typeName": "Chatbot Agent",
        "exp": 150,
        "expMax": 300,
        "level": 2,
        "badge_url": "https://cdn.example.com/badges/silver.png",
        "model_id": "gpt-4o",
        "provider_type": "system",
        "active": true,
        "createdAt": 1672531200000,
        "updatedAt": 1672531300000
      }
    ],
    "meta": {
      "totalItems": 25,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 3,
      "currentPage": 1
    }
  },
  "message": "Lấy danh sách agent thành công"
}
```

## Query Parameters

### **Pagination**
- `page`: Số trang (≥1, default: 1)
- `limit`: Số items per page (1-100, default: 10)

### **Search & Filter**
- `search`: Tìm kiếm theo tên agent (ILIKE pattern)
- `typeId`: Lọc theo ID loại agent (chỉ có trong full list)

### **Sorting**
- `sortBy`: Trường sắp xếp
  - `'name'`: Sắp xếp theo tên
  - `'createdAt'`: Sắp xếp theo thời gian tạo (default)
- `sortDirection`: Hướng sắp xếp
  - `'ASC'`: Tăng dần
  - `'DESC'`: Giảm dần (default)

## Response Fields

### **Simple List Response (AgentSimpleListDto)**
| Field | Type | Description |
|-------|------|-------------|
| `id` | string | ID của agent |
| `name` | string | Tên agent |
| `avatar` | string? | URL avatar (nullable) |

### **Full List Response (AgentListItemDto)**
| Field | Type | Description |
|-------|------|-------------|
| `id` | string | ID của agent |
| `name` | string | Tên agent |
| `avatar` | string? | URL avatar (nullable) |
| `typeId` | number | ID loại agent |
| `typeName` | string | Tên loại agent |
| `exp` | number | Kinh nghiệm hiện tại |
| `expMax` | number | Kinh nghiệm tối đa để lên cấp |
| `level` | number | Cấp độ hiện tại |
| `badge_url` | string | URL khung rank |
| `model_id` | string? | ID model đang sử dụng |
| `provider_type` | string? | Loại provider |
| `active` | boolean | Trạng thái hoạt động |
| `createdAt` | number | Timestamp tạo (millis) |
| `updatedAt` | number | Timestamp cập nhật (millis) |

## Error Codes

- `40120`: Lỗi khi thao tác với tài nguyên agent
- `50001`: Lỗi server nội bộ

## Security

- Tất cả APIs yêu cầu JWT authentication
- User chỉ có thể xem agents thuộc về mình
- Soft-deleted agents không được hiển thị

## Performance Features

### **Database Optimization**
- Sử dụng JOIN queries thay vì N+1 queries
- Index trên các trường search và sort
- Pagination để giảm tải

### **CDN Integration**
- Avatar URLs được tạo với CDN service
- URLs có thời hạn 1 ngày
- Graceful fallback nếu CDN lỗi

### **Caching Strategy**
- Type agent names được cache
- Rank information được cache
- Avatar URLs có expiration

## Business Logic

### **Simple List Use Cases**
- Dropdown selections
- Quick previews
- Mobile list views
- Performance-critical scenarios

### **Full List Use Cases**
- Main dashboard
- Agent management pages
- Detailed listings
- Analytics views

### **Filtering Logic**
- Search: ILIKE pattern matching trên tên
- TypeId: Exact match với type_id
- Active agents only (deleted_at IS NULL)
- User ownership validation

### **Sorting Logic**
- Default: Newest first (createdAt DESC)
- Name sorting: Alphabetical order
- Stable sorting for consistent results

### **Avatar Handling**
- CDN URLs với 1-day expiration
- Null-safe handling
- Fallback cho missing avatars

## TODO Items

### **Model Resolution**
- Implement actual model_id resolution từ relationships
- Add provider_type mapping logic
- Handle multiple model types (user/system/fine-tune)

### **Rank System**
- Calculate expMax từ rank configuration
- Determine level từ exp points
- Generate badge_url từ rank system

### **Performance Optimization**
- Add database indexes
- Implement query result caching
- Optimize JOIN queries

### **Enhanced Features**
- Add more filter options
- Support advanced search
- Add bulk operations
