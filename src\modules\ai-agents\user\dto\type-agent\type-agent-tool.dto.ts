import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin tool trong type agent detail (đơn giản)
 */
export class TypeAgentToolDto {
  /**
   * ID của tool
   */
  @ApiProperty({
    description: 'ID của tool',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  /**
   * Tên của tool
   */
  @ApiProperty({
    description: 'Tên của tool',
    example: 'Công cụ tìm kiếm',
  })
  name: string;

  /**
   * Mô tả về tool
   */
  @ApiProperty({
    description: 'Mô tả về tool',
    example: 'Công cụ giúp tìm kiếm thông tin từ nhiều nguồn',
    nullable: true,
  })
  description: string | null;

  /**
   * Tên phiên bản mặc định
   */
  @ApiProperty({
    description: 'Tên phiên bản mặc định',
    example: 'v1.0.0',
    nullable: true,
  })
  versionName: string | null;
}
