import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import {
  getProfile,
  updateProfile,
  ProfileResponseDto,
  UpdateProfileDto
} from '../api/profile.api';

/**
 * Service layer cho Profile - chứa business logic
 */

/**
 * <PERSON><PERSON><PERSON> thông tin profile với business logic
 * @param agentId ID của agent
 * @returns Promise với response từ API
 */
export const getProfileWithBusinessLogic = async (
  agentId: string
): Promise<ApiResponse<ProfileResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Validate agentId format
  // - Check permissions
  // - Transform response data

  return getProfile(agentId);
};

/**
 * Cập nhật profile với business logic
 * @param agentId ID của agent
 * @param data Dữ liệu cập nhật
 * @returns Promise với response từ API
 */
export const updateProfileWithBusinessLogic = async (
  agentId: string,
  data: UpdateProfileDto
): Promise<ApiResponse<ProfileResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Validate input data
  // - Transform data format
  // - Set default values
  // - Pre-processing

  // Validate gender
  if (data.gender) {
    const allowedGenders = ['MALE', 'FEMALE', 'OTHER'];
    if (!allowedGenders.includes(data.gender.toUpperCase())) {
      throw new Error('Invalid gender. Must be MALE, FEMALE, or OTHER');
    }
    data.gender = data.gender.toUpperCase();
  }

  // Validate birth date format
  if (data.birthDate) {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(data.birthDate)) {
      throw new Error('Invalid birth date format. Must be YYYY-MM-DD');
    }
    
    const birthDate = new Date(data.birthDate);
    const today = new Date();
    if (birthDate > today) {
      throw new Error('Birth date cannot be in the future');
    }
  }

  // Validate skills array
  if (data.skills) {
    if (!Array.isArray(data.skills)) {
      throw new Error('Skills must be an array');
    }
    if (data.skills.length > 20) {
      throw new Error('Maximum 20 skills allowed');
    }
    // Remove duplicates and empty strings
    data.skills = [...new Set(data.skills.filter(skill => skill.trim().length > 0))];
  }

  // Validate personality array
  if (data.personality) {
    if (!Array.isArray(data.personality)) {
      throw new Error('Personality must be an array');
    }
    if (data.personality.length > 10) {
      throw new Error('Maximum 10 personality traits allowed');
    }
    // Remove duplicates and empty strings
    data.personality = [...new Set(data.personality.filter(trait => trait.trim().length > 0))];
  }

  // Validate languages array
  if (data.languages) {
    if (!Array.isArray(data.languages)) {
      throw new Error('Languages must be an array');
    }
    if (data.languages.length > 10) {
      throw new Error('Maximum 10 languages allowed');
    }
    // Remove duplicates and empty strings
    data.languages = [...new Set(data.languages.filter(lang => lang.trim().length > 0))];
  }

  // Validate text field lengths
  if (data.position && data.position.length > 100) {
    throw new Error('Position must be less than 100 characters');
  }
  if (data.education && data.education.length > 200) {
    throw new Error('Education must be less than 200 characters');
  }
  if (data.country && data.country.length > 50) {
    throw new Error('Country must be less than 50 characters');
  }

  return updateProfile(agentId, data);
};
