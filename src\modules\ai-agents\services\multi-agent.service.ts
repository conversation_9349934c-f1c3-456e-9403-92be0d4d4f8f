import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import {
  getMultiAgentRelations,
  createMultiAgentRelation,
  updateMultiAgentRelation,
  deleteMultiAgentRelation,
  bulkUpdateMultiAgentRelations,
  MultiAgentResponseDto,
  CreateMultiAgentRelationDto,
  UpdateMultiAgentRelationDto,
  BulkUpdateMultiAgentDto,
  MultiAgentRelationDto
} from '../api/multi-agent.api';

/**
 * Service layer cho Multi-Agent - chứa business logic
 */

/**
 * L<PERSON><PERSON> danh sách quan hệ multi-agent với business logic
 * @param agentId ID của agent
 * @returns Promise với response từ API
 */
export const getMultiAgentRelationsWithBusinessLogic = async (
  agentId: string
): Promise<ApiResponse<MultiAgentResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Validate agentId format
  // - Check permissions
  // - Transform response data

  return getMultiAgentRelations(agentId);
};

/**
 * Tạ<PERSON> quan hệ multi-agent với business logic
 * @param agentId ID của agent cha
 * @param data Dữ liệu quan hệ mới
 * @returns Promise với response từ API
 */
export const createMultiAgentRelationWithBusinessLogic = async (
  agentId: string,
  data: CreateMultiAgentRelationDto
): Promise<ApiResponse<MultiAgentRelationDto>> => {
  // Business logic có thể bao gồm:
  // - Validate input data
  // - Check for circular references
  // - Validate permissions

  // Validate child agent ID
  if (!data.childAgentId || typeof data.childAgentId !== 'string') {
    throw new Error('Child agent ID is required');
  }

  // Prevent self-reference
  if (data.childAgentId === agentId) {
    throw new Error('Agent cannot reference itself');
  }

  // Validate prompt length
  if (data.prompt && data.prompt.length > 5000) {
    throw new Error('Prompt must be less than 5000 characters');
  }

  return createMultiAgentRelation(agentId, data);
};

/**
 * Cập nhật quan hệ multi-agent với business logic
 * @param parentAgentId ID của agent cha
 * @param childAgentId ID của agent con
 * @param data Dữ liệu cập nhật
 * @returns Promise với response từ API
 */
export const updateMultiAgentRelationWithBusinessLogic = async (
  parentAgentId: string,
  childAgentId: string,
  data: UpdateMultiAgentRelationDto
): Promise<ApiResponse<MultiAgentRelationDto>> => {
  // Business logic có thể bao gồm:
  // - Validate input data
  // - Check permissions
  // - Validate relationships

  // Validate child agent ID
  if (!data.childAgentId || typeof data.childAgentId !== 'string') {
    throw new Error('Child agent ID is required');
  }

  // Prevent self-reference
  if (data.childAgentId === parentAgentId) {
    throw new Error('Agent cannot reference itself');
  }

  // Validate prompt length
  if (data.prompt && data.prompt.length > 5000) {
    throw new Error('Prompt must be less than 5000 characters');
  }

  return updateMultiAgentRelation(parentAgentId, childAgentId, data);
};

/**
 * Xóa quan hệ multi-agent với business logic
 * @param parentAgentId ID của agent cha
 * @param childAgentId ID của agent con
 * @returns Promise với response từ API
 */
export const deleteMultiAgentRelationWithBusinessLogic = async (
  parentAgentId: string,
  childAgentId: string
): Promise<ApiResponse<void>> => {
  // Business logic có thể bao gồm:
  // - Check permissions
  // - Validate relationship exists
  // - Cleanup related data

  return deleteMultiAgentRelation(parentAgentId, childAgentId);
};

/**
 * Cập nhật hàng loạt quan hệ multi-agent với business logic
 * @param agentId ID của agent cha
 * @param data Dữ liệu cập nhật hàng loạt
 * @returns Promise với response từ API
 */
export const bulkUpdateMultiAgentRelationsWithBusinessLogic = async (
  agentId: string,
  data: BulkUpdateMultiAgentDto
): Promise<ApiResponse<MultiAgentResponseDto>> => {
  // Business logic có thể bao gồm:
  // - Validate input data
  // - Check for circular references
  // - Validate limits

  // Validate relations array
  if (!Array.isArray(data.relations)) {
    throw new Error('Relations must be an array');
  }

  if (data.relations.length > 20) {
    throw new Error('Maximum 20 relations allowed');
  }

  // Validate each relation
  const childAgentIds = new Set<string>();
  
  for (const relation of data.relations) {
    // Validate child agent ID
    if (!relation.childAgentId || typeof relation.childAgentId !== 'string') {
      throw new Error('Child agent ID is required for all relations');
    }

    // Prevent self-reference
    if (relation.childAgentId === agentId) {
      throw new Error('Agent cannot reference itself');
    }

    // Check for duplicate child agents
    if (childAgentIds.has(relation.childAgentId)) {
      throw new Error(`Duplicate child agent ID: ${relation.childAgentId}`);
    }
    childAgentIds.add(relation.childAgentId);

    // Validate prompt length
    if (relation.prompt && relation.prompt.length > 5000) {
      throw new Error(`Prompt for agent ${relation.childAgentId} must be less than 5000 characters`);
    }
  }

  return bulkUpdateMultiAgentRelations(agentId, data);
};
