import { Url } from '@modules/data/url/entities';
import { Media } from '@modules/data/media/entities';
import { UserProduct } from '@modules/business/entities';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils';
import {
  AgentUrlResponseDto,
  AgentMediaResponseDto,
  AgentProductResponseDto
} from '../dto/resource';

/**
 * Mapper cho các tài nguyên của agent
 */
export class AgentResourceMapper {
  /**
   * Chuyển đổi Url entity sang AgentUrlResponseDto
   * @param url Url entity
   * @returns AgentUrlResponseDto
   */
  static toUrlResponseDto(url: Url): AgentUrlResponseDto {
    return {
      id: url.id,
      url: url.url,
      title: url.title,
      createdAt: url.createdAt,
    };
  }

  /**
   * Chuyển đổi danh sách Url entity sang danh sách AgentUrlResponseDto
   * @param urls Danh sách Url entity
   * @returns Danh sách AgentUrlResponseDto
   */
  static toUrlResponseDtos(urls: Url[]): AgentUrlResponseDto[] {
    return urls.map(url => this.toUrlResponseDto(url));
  }

  /**
   * Chuyển đổi Media entity sang AgentMediaResponseDto
   * @param media Media entity
   * @param cdnService CDN service để tạo URL
   * @returns AgentMediaResponseDto
   */
  static toMediaResponseDto(media: Media, cdnService: CdnService): AgentMediaResponseDto {
    // Tạo URL CDN cho media
    let url = '';
    if (media.storageKey) {
      try {
        const generatedUrl = cdnService.generateUrlView(media.storageKey, TimeIntervalEnum.ONE_DAY);
        url = generatedUrl || '';
      } catch (error) {
        console.warn(`Không thể tạo URL CDN cho media ${media.id}: ${error.message}`);
        url = '';
      }
    }

    return {
      id: media.id,
      name: media.name,
      description: media.description,
      size: media.size,
      url,
      createdAt: media.createdAt,
    };
  }

  /**
   * Chuyển đổi danh sách Media entity sang danh sách AgentMediaResponseDto
   * @param medias Danh sách Media entity
   * @param cdnService CDN service để tạo URL
   * @returns Danh sách AgentMediaResponseDto
   */
  static toMediaResponseDtos(medias: Media[], cdnService: CdnService): AgentMediaResponseDto[] {
    return medias.map(media => this.toMediaResponseDto(media, cdnService));
  }

  /**
   * Chuyển đổi UserProduct entity sang AgentProductResponseDto
   * @param product UserProduct entity
   * @returns AgentProductResponseDto
   */
  static toProductResponseDto(product: UserProduct): AgentProductResponseDto {
    // Lấy URL hình ảnh đầu tiên từ trường images (JSONB)
    let imageUrl = '';
    if (product.images && Array.isArray(product.images) && product.images.length > 0) {
      imageUrl = product.images[0].url || product.images[0].key || '';
    }

    return {
      id: product.id,
      name: product.name,
      imageUrl,
      createdAt: product.createdAt,
    };
  }

  /**
   * Chuyển đổi danh sách UserProduct entity sang danh sách AgentProductResponseDto
   * @param products Danh sách UserProduct entity
   * @returns Danh sách AgentProductResponseDto
   */
  static toProductResponseDtos(products: UserProduct[]): AgentProductResponseDto[] {
    return products.map(product => this.toProductResponseDto(product));
  }
}
