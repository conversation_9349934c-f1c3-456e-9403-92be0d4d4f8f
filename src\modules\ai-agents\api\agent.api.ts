import { apiClient } from '@/shared/api';
import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { QueryDto } from '@/shared/dto/request/query.dto';
import { getMockAgentDetail, getMockTypeAgentDetail } from '../data/mockAgentDetail';

// Import types từ thư mục types
import {
  GetAgentsQueryDto,
  GetTypeAgentsQueryDto,
  GetBaseModelsQueryDto,
  AgentStatisticsQueryDto,
  CreateAgentDto,
  CreateAgentResponseDto,
  AgentStatisticsResponseDto,
  UpdateAgentVectorStoreDto,
  TypeAgentListResponse,
  BaseModelListResponse,
  AgentDetailDto,
  TypeAgentDetailDto,
  BaseModelUserResponseDto,
  UserProviderModelResponseDto
} from '../types';

/**
 * API functions cho Agent management
 * Tương ứng với AgentUserController trong backend
 */

// Agent Simple List DTOs
export interface AgentSimpleListDto {
  id: string;
  avatar?: string;
  name: string;
}

export interface AgentSimpleQueryDto extends QueryDto {
  search?: string;
}

// Agent List DTOs
export interface AgentListItemDto {
  id: string;
  avatar?: string;
  name: string;
  typeAgentName: string;
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
}

// Tất cả interfaces đã được chuyển sang thư mục types
// File này chỉ chứa các API functions

/**
 * Lấy danh sách agent đơn giản (chỉ id, avatar, name)
 * GET /user/agents/simple
 */
export const getSimpleAgents = async (
  params?: AgentSimpleQueryDto
): Promise<ApiResponse<PaginatedResult<AgentSimpleListDto>>> => {
  return apiClient.get('/user/agents/simple', { params });
};

/**
 * Lấy danh sách agents đầy đủ
 * GET /user/agents
 */
export const getAgents = async (
  params?: GetAgentsQueryDto
): Promise<ApiResponse<PaginatedResult<AgentListItemDto>>> => {
  return apiClient.get('/user/agents', { params });
};

/**
 * Lấy chi tiết agent theo ID
 * @param id ID của agent
 * @returns Promise với response từ API
 */
export const getAgentDetail = async (
  id: string
): Promise<ApiResponse<AgentDetailDto>> => {
  // MOCK: Sử dụng mock data cho development
  if (process.env.NODE_ENV === 'development') {
    const mockData = getMockAgentDetail(id);
    return Promise.resolve({
      success: true,
      code: 200,
      message: 'Agent detail retrieved successfully',
      result: mockData,
      timestamp: new Date().toISOString(),
    });
  }

  return apiClient.get(`/user/agents/${id}`);
};

/**
 * Tạo agent mới với cấu trúc modular
 * POST /user/agents
 */
export const createAgent = async (
  data: CreateAgentDto
): Promise<ApiResponse<CreateAgentResponseDto>> => {
  return apiClient.post('/user/agents', data);
};

/**
 * Xóa agent (soft delete)
 * DELETE /user/agents/{id}
 */
export const deleteAgent = async (id: string): Promise<ApiResponse<void>> => {
  return apiClient.delete(`/user/agents/${id}`);
};

/**
 * Bật/tắt trạng thái hoạt động của agent
 * PATCH /user/agents/{id}/active
 */
export const toggleAgentActive = async (
  id: string
): Promise<ApiResponse<{ active: boolean }>> => {
  return apiClient.patch(`/user/agents/${id}/active`);
};

/**
 * Lấy thống kê agent
 * @param id ID của agent
 * @param params Tham số query thống kê
 * @returns Promise với response từ API
 */
export const getAgentStatistics = async (
  id: string,
  params?: AgentStatisticsQueryDto
): Promise<ApiResponse<AgentStatisticsResponseDto>> => {
  return apiClient.get(`/user/agents/${id}/statistics`, { params });
};

/**
 * Cập nhật vector store cho agent
 * @param id ID của agent
 * @param data Dữ liệu vector store
 * @returns Promise với response từ API
 */
export const updateAgentVectorStore = async (
  id: string,
  data: UpdateAgentVectorStoreDto
): Promise<ApiResponse<void>> => {
  return apiClient.patch(`/user/agents/${id}/vector-store`, data);
};

/**
 * Lấy danh sách type agents
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getTypeAgents = async (
  params?: GetTypeAgentsQueryDto
): Promise<ApiResponse<TypeAgentListResponse>> => {
  return apiClient.get('/user/type-agents', { params });
};

/**
 * Lấy chi tiết type agent theo ID
 * @param id ID của type agent
 * @returns Promise với response từ API
 */
export const getTypeAgentDetail = async (
  id: number
): Promise<ApiResponse<TypeAgentDetailDto>> => {
  // MOCK: Sử dụng mock data cho development
  if (process.env.NODE_ENV === 'development') {
    const mockData = getMockTypeAgentDetail(id);
    return Promise.resolve({
      success: true,
      code: 200,
      message: 'Type agent detail retrieved successfully',
      result: mockData,
      timestamp: new Date().toISOString(),
    });
  }

  return apiClient.get(`/user/type-agents/${id}`);
};

// Removed: createTypeAgent, updateTypeAgent, deleteTypeAgent APIs

/**
 * Lấy danh sách base models từ hệ thống RedAI (mặc định)
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getBaseModels = async (
  params?: GetBaseModelsQueryDto
): Promise<ApiResponse<BaseModelListResponse>> => {
  return apiClient.get('/user/base-models', { params });
};

/**
 * Lấy danh sách base models từ provider cụ thể của user
 * @param providerId ID của provider
 * @returns Promise với response từ API
 */
export const getBaseModelsByUserProvider = async (
  providerId: string
): Promise<ApiResponse<BaseModelUserResponseDto[]>> => {
  return apiClient.get(`/user/base-models/provider/${providerId}`);
};

/**
 * Lấy danh sách user providers
 * @param params Tham số truy vấn
 * @returns Promise với response từ API
 */
export const getUserProviders = async (
  params?: QueryDto
): Promise<ApiResponse<PaginatedResult<UserProviderModelResponseDto>>> => {
  return apiClient.get('/user/provider-model', { params });
};


